<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产线节拍器 - 演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .title {
            color: white;
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .card-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }

        .counter-display {
            text-align: center;
            margin: 30px 0;
        }

        .count-number {
            font-size: 96px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 10px;
        }

        .count-label {
            font-size: 18px;
            color: #666;
        }

        .progress-section {
            margin: 25px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #52c41a);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .timer-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }

        .timer-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }

        .timer-label {
            font-weight: bold;
            color: #333;
        }

        .timer-value {
            font-size: 18px;
            font-weight: bold;
        }

        .countdown {
            color: #ff4d4f;
            font-size: 24px;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #d9d9d9;
            transform: translateY(-2px);
        }

        .target-input {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            border: 2px solid #d9d9d9;
            border-radius: 8px;
            margin: 15px 0;
        }

        .target-display {
            text-align: center;
            padding: 20px;
        }

        .target-number {
            font-size: 48px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 10px;
        }

        .speed-section {
            text-align: center;
            margin: 20px 0;
        }

        .speed-value {
            font-size: 36px;
            font-weight: bold;
            color: #52c41a;
            margin: 15px 0;
        }

        .speed-comparison {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4d4f;
        }

        .status-dot.connected {
            background: #52c41a;
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 36px;
            }
            
            .count-number {
                font-size: 72px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 标题 -->
        <div class="header">
            <h1 class="title">生产线节拍器</h1>
            <p class="subtitle">实时监控生产数量与速度，激发团队竞争力</p>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">演示模式 - 串口未连接</span>
            </div>
            <div class="status-item">
                <span>当前时间: </span>
                <span id="currentTime"></span>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-grid">
            <!-- 左侧 - 计数器 -->
            <div class="card">
                <div class="card-header">
                    <span class="card-icon">🔢</span>
                    <h2 class="card-title" style="color: #1890ff;">生产计数器</h2>
                </div>

                <div class="counter-display">
                    <div class="count-number" id="countNumber">0</div>
                    <div class="count-label">当前产量 (个)</div>
                </div>

                <div class="progress-section">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>目标进度</span>
                        <span id="progressText">0/100 (0%)</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                </div>

                <div class="timer-section">
                    <div class="timer-row">
                        <span class="timer-label">⏰ 周期开始时间:</span>
                        <span class="timer-value" id="cycleStart"></span>
                    </div>
                    <div class="timer-row">
                        <span class="timer-label">⏱️ 距离重置:</span>
                        <span class="timer-value countdown" id="countdown"></span>
                    </div>
                </div>

                <div class="buttons">
                    <button class="btn btn-primary" onclick="incrementCount()">手动计数 +1</button>
                    <button class="btn btn-secondary" onclick="resetCount()">重置计数</button>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div style="display: flex; flex-direction: column; gap: 30px;">
                <!-- 目标设置 -->
                <div class="card">
                    <div class="card-header">
                        <span class="card-icon">🎯</span>
                        <h2 class="card-title" style="color: #1890ff;">生产目标</h2>
                    </div>

                    <div class="target-display" id="targetDisplay">
                        <div class="target-number" id="targetNumber">100</div>
                        <div>个/10分钟</div>
                        <button class="btn btn-secondary" onclick="editTarget()" style="margin-top: 15px;">编辑目标</button>
                    </div>

                    <div id="targetEdit" style="display: none;">
                        <input type="number" class="target-input" id="targetInput" placeholder="输入目标数量" min="0" max="9999">
                        <div class="buttons">
                            <button class="btn btn-primary" onclick="saveTarget()">保存</button>
                            <button class="btn btn-secondary" onclick="cancelEdit()">取消</button>
                        </div>
                    </div>
                </div>

                <!-- 速度显示 -->
                <div class="card">
                    <div class="card-header">
                        <span class="card-icon">🚀</span>
                        <h2 class="card-title" style="color: #52c41a;">生产速度</h2>
                    </div>

                    <div class="speed-section">
                        <div>当前周期速度</div>
                        <div class="speed-value" id="currentSpeed">0秒/个</div>
                    </div>

                    <div class="speed-comparison">
                        <div style="font-weight: bold; margin-bottom: 10px;">与上个周期对比</div>
                        <div id="speedComparison">首次记录</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 应用状态
        let currentCount = 0;
        let target = 100;
        let cycleStartTime = new Date();
        let previousSpeed = 0;
        let isEditingTarget = false;

        // 初始化周期开始时间（每10分钟的开始）
        function initializeCycleStart() {
            const now = new Date();
            const minutes = now.getMinutes();
            const cycleMinutes = Math.floor(minutes / 10) * 10;
            
            cycleStartTime = new Date(now);
            cycleStartTime.setMinutes(cycleMinutes, 0, 0);
        }

        // 获取下一个周期开始时间
        function getNextCycleStart() {
            const nextStart = new Date(cycleStartTime);
            nextStart.setMinutes(nextStart.getMinutes() + 10);
            return nextStart;
        }

        // 格式化时间
        function formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 格式化倒计时
        function formatCountdown(ms) {
            const totalSeconds = Math.ceil(ms / 1000);
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 计算生产速度
        function calculateSpeed() {
            if (currentCount === 0) return 0;
            const now = new Date();
            const elapsedMinutes = (now - cycleStartTime) / (1000 * 60);
            return (elapsedMinutes * 60) / currentCount; // 秒/个
        }

        // 格式化速度
        function formatSpeed(speedInSeconds) {
            if (speedInSeconds === 0) return '0秒/个';
            if (speedInSeconds >= 60) {
                const minutes = Math.floor(speedInSeconds / 60);
                const seconds = Math.round(speedInSeconds % 60);
                return `${minutes}分${seconds}秒/个`;
            } else {
                return `${Math.round(speedInSeconds)}秒/个`;
            }
        }

        // 更新显示
        function updateDisplay() {
            // 更新计数
            document.getElementById('countNumber').textContent = currentCount;
            
            // 更新进度
            const progress = target > 0 ? Math.min((currentCount / target) * 100, 100) : 0;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `${currentCount}/${target} (${progress.toFixed(1)}%)`;
            
            // 更新时间
            document.getElementById('currentTime').textContent = formatTime(new Date());
            document.getElementById('cycleStart').textContent = formatTime(cycleStartTime);
            
            // 更新倒计时
            const nextCycle = getNextCycleStart();
            const timeToNext = nextCycle.getTime() - new Date().getTime();
            document.getElementById('countdown').textContent = formatCountdown(timeToNext);
            
            // 更新速度
            const currentSpeed = calculateSpeed();
            document.getElementById('currentSpeed').textContent = formatSpeed(currentSpeed);
            
            // 更新速度对比
            let comparisonText = '首次记录';
            if (previousSpeed > 0 && currentSpeed > 0) {
                if (currentSpeed < previousSpeed) {
                    const improvement = ((previousSpeed - currentSpeed) / previousSpeed * 100).toFixed(1);
                    comparisonText = `⬆️ 提速 ${improvement}%`;
                } else if (currentSpeed > previousSpeed) {
                    const decline = ((currentSpeed - previousSpeed) / previousSpeed * 100).toFixed(1);
                    comparisonText = `⬇️ 减速 ${decline}%`;
                } else {
                    comparisonText = '➡️ 速度相同';
                }
            }
            document.getElementById('speedComparison').textContent = comparisonText;
            
            // 检查是否需要重置周期
            if (timeToNext <= 0) {
                resetCycle();
            }
        }

        // 重置周期
        function resetCycle() {
            previousSpeed = calculateSpeed();
            currentCount = 0;
            initializeCycleStart();
            alert('新的10分钟周期开始！');
        }

        // 增加计数
        function incrementCount() {
            currentCount++;
            updateDisplay();
        }

        // 重置计数
        function resetCount() {
            if (confirm('确定要重置计数吗？')) {
                currentCount = 0;
                updateDisplay();
            }
        }

        // 编辑目标
        function editTarget() {
            document.getElementById('targetDisplay').style.display = 'none';
            document.getElementById('targetEdit').style.display = 'block';
            document.getElementById('targetInput').value = target;
            document.getElementById('targetInput').focus();
        }

        // 保存目标
        function saveTarget() {
            const newTarget = parseInt(document.getElementById('targetInput').value);
            if (newTarget >= 0 && newTarget <= 9999) {
                target = newTarget;
                document.getElementById('targetNumber').textContent = target;
                cancelEdit();
                updateDisplay();
                alert(`目标已设置为 ${target} 个/10分钟`);
            } else {
                alert('请输入0-9999之间的数字');
            }
        }

        // 取消编辑
        function cancelEdit() {
            document.getElementById('targetDisplay').style.display = 'block';
            document.getElementById('targetEdit').style.display = 'none';
        }

        // 初始化
        function init() {
            initializeCycleStart();
            updateDisplay();
            
            // 每秒更新一次
            setInterval(updateDisplay, 1000);
            
            // 支持回车键保存目标
            document.getElementById('targetInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveTarget();
                }
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
