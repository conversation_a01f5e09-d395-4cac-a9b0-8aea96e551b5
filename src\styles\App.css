/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
}

/* 主应用容器 */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px;
}

.app-header {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-title {
  color: white !important;
  margin: 0 !important;
  font-size: 36px !important;
  font-weight: bold !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  color: rgba(255, 255, 255, 0.9) !important;
  margin: 8px 0 0 0 !important;
  font-size: 16px !important;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.right-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 状态栏样式 */
.status-bar {
  background: rgba(255, 255, 255, 0.95) !important;
  border: none !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.status-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.status-time {
  margin-left: auto;
}

/* 计数器卡片样式 */
.counter-card {
  background: rgba(255, 255, 255, 0.95) !important;
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden;
}

.counter-card .ant-card-head {
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  border-bottom: none;
  padding: 16px 24px;
}

.counter-card .ant-card-head-title {
  color: white;
}

.counter-card .ant-card-extra {
  color: white;
}

.main-counter-section {
  text-align: center;
}

.counter-display {
  margin-bottom: 24px;
}

.target-comparison {
  margin-top: 24px;
}

.cycle-info-section {
  margin: 24px 0;
}

.counter-actions {
  margin-top: 24px;
}

/* 目标设置卡片样式 */
.target-setting-card {
  background: rgba(255, 255, 255, 0.95) !important;
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
}

.target-setting-card .ant-card-head {
  background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
  border-bottom: none;
  padding: 16px 24px;
}

.target-setting-card .ant-card-head-title {
  color: white;
}

.target-setting-card .ant-card-extra {
  color: white;
}

.target-content {
  text-align: center;
}

.target-display .target-number {
  margin-bottom: 12px;
}

.target-input-section {
  text-align: left;
}

/* 速度显示卡片样式 */
.speed-display-card {
  background: rgba(255, 255, 255, 0.95) !important;
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
}

.speed-display-card .ant-card-head {
  background: linear-gradient(135deg, #52c41a 0%, #faad14 100%);
  border-bottom: none;
  padding: 16px 24px;
}

.speed-display-card .ant-card-head-title {
  color: white;
}

.current-speed-section {
  text-align: center;
  padding: 16px 0;
}

.speed-value {
  margin-top: 12px;
}

.speed-comparison-section {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.target-progress-section {
  padding: 16px;
  background: #f0f8ff;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .app-title {
    font-size: 28px !important;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }
  
  .app-title {
    font-size: 24px !important;
  }
  
  .status-bar-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .status-time {
    margin-left: 0;
    text-align: center;
  }
}

/* 动画效果 */
.counter-card,
.target-setting-card,
.speed-display-card,
.status-bar {
  transition: all 0.3s ease;
}

.counter-card:hover,
.target-setting-card:hover,
.speed-display-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15) !important;
}

/* 进度条自定义样式 */
.ant-progress-bg {
  transition: all 0.3s ease;
}

/* 按钮动画 */
.ant-btn {
  transition: all 0.3s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
}

/* 统计数字动画 */
.ant-statistic-content-value {
  transition: all 0.3s ease;
}

/* 标签动画 */
.ant-tag {
  transition: all 0.3s ease;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
