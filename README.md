# 生产线节拍器

一个专为生产线设计的现代化节拍器应用，用于实时监控生产数量和速度，激发员工竞争力。

## 功能特点

### 🔢 智能计数
- 通过串口COM8接收网页采集数据进行自动计数
- 支持手动计数功能（测试模式）
- 实时显示当前生产数量

### ⏰ 10分钟周期管理
- 按系统时间每10分钟自动重置计数
- 精确的倒计时显示
- 自动周期切换提醒

### 🎯 目标设置与对比
- 用户可自定义10分钟生产目标
- 实时显示目标完成进度
- 直观的进度条和百分比显示

### 🚀 生产速度分析
- 计算每个产品的平均生产时间
- 显示当前周期与上个周期的速度对比
- 速度提升/下降百分比显示

### 🎨 美观界面
- 现代化渐变背景设计
- 丰富的色彩搭配
- 响应式布局，适配不同屏幕
- 流畅的动画效果

## 技术架构

- **前端框架**: React 18 + Vite
- **UI组件库**: Ant Design 5
- **桌面应用**: Electron 28
- **串口通信**: SerialPort
- **样式**: CSS3 + 动画效果

## 安装和运行

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式运行
```bash
# 启动Web开发服务器
npm run dev

# 启动Electron桌面应用（开发模式）
npm run electron-dev
```

### 生产构建
```bash
# 构建Web版本
npm run build

# 构建桌面应用
npm run dist
```

## 使用说明

### 1. 串口连接
- 应用启动后会自动尝试连接COM8端口
- 可在状态栏手动选择其他串口
- 支持串口状态实时监控

### 2. 设置生产目标
- 点击"目标设置"卡片的编辑按钮
- 输入合理的10分钟生产目标
- 保存后立即生效

### 3. 监控生产进度
- 主计数器显示当前生产数量
- 进度条显示目标完成百分比
- 倒计时显示距离下次重置的时间

### 4. 查看速度分析
- 实时显示当前生产速度（秒/个）
- 与上个周期速度对比
- 目标完成度分析

### 5. 测试功能
- 在浏览器环境中可使用"手动计数"按钮测试
- 支持手动重置计数功能

## 串口数据格式

应用接收COM8端口的数据，每接收到一条数据就计数一次。支持的数据格式：
- 任何以`\r\n`结尾的文本数据
- 波特率：9600
- 数据位：8
- 停止位：1
- 校验位：无

## 文件结构

```
节拍器/
├── package.json              # 项目配置
├── vite.config.js            # Vite配置
├── electron/                 # Electron主进程
│   ├── main.js              # 主进程入口
│   └── preload.js           # 预加载脚本
├── src/                     # 源代码
│   ├── App.jsx              # 主应用组件
│   ├── components/          # React组件
│   │   ├── Counter.jsx      # 计数器组件
│   │   ├── TargetSetting.jsx # 目标设置组件
│   │   ├── SpeedDisplay.jsx  # 速度显示组件
│   │   └── StatusBar.jsx     # 状态栏组件
│   ├── hooks/               # 自定义Hooks
│   │   ├── useSerialPort.js  # 串口通信Hook
│   │   └── useTimer.js       # 定时器Hook
│   ├── utils/               # 工具函数
│   │   └── timeUtils.js      # 时间工具
│   ├── styles/              # 样式文件
│   │   └── App.css           # 主样式
│   └── main.jsx             # 入口文件
├── public/                  # 静态资源
│   └── index.html           # HTML模板
└── README.md               # 说明文档
```

## 故障排除

### 串口连接问题
1. 确认COM8端口存在且未被其他程序占用
2. 检查串口参数设置是否正确
3. 尝试重新连接或选择其他端口

### 应用无法启动
1. 确认Node.js版本符合要求
2. 删除node_modules文件夹后重新安装依赖
3. 检查防火墙和杀毒软件设置

### 计数不准确
1. 检查串口数据格式是否正确
2. 确认数据发送频率是否合理
3. 查看控制台日志排查问题

## 开发说明

### 添加新功能
1. 在相应的组件文件中添加功能
2. 更新相关的Hook和工具函数
3. 添加必要的样式和动画

### 自定义样式
- 主要样式在`src/styles/App.css`中
- 支持CSS3动画和渐变效果
- 响应式设计适配移动设备

### 扩展串口功能
- 修改`electron/main.js`中的串口配置
- 更新`src/hooks/useSerialPort.js`中的数据处理逻辑

## 许可证

MIT License

## 支持

如有问题或建议，请联系开发团队。
