import React, { useState } from 'react'
import { Card, InputNumber, Button, Space, Typography, message } from 'antd'
import { TargetOutlined, EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

/**
 * 目标设置组件 - 允许用户设置生产目标
 */
function TargetSetting({ target, onTargetChange }) {
  const [isEditing, setIsEditing] = useState(false)
  const [tempTarget, setTempTarget] = useState(target)

  const handleEdit = () => {
    setTempTarget(target)
    setIsEditing(true)
  }

  const handleSave = () => {
    if (tempTarget < 0) {
      message.error('目标数量不能为负数')
      return
    }
    
    onTargetChange(tempTarget)
    setIsEditing(false)
    message.success('目标设置已保存')
  }

  const handleCancel = () => {
    setTempTarget(target)
    setIsEditing(false)
  }

  return (
    <Card 
      className="target-setting-card"
      title={
        <Space>
          <TargetOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
            生产目标
          </Title>
        </Space>
      }
      extra={
        !isEditing && (
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={handleEdit}
            style={{ color: '#1890ff' }}
          >
            编辑
          </Button>
        )
      }
      bodyStyle={{ padding: '20px' }}
    >
      <div className="target-content">
        {isEditing ? (
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div className="target-input-section">
              <Text strong style={{ fontSize: '16px', marginBottom: '8px', display: 'block' }}>
                设置10分钟目标产量:
              </Text>
              <InputNumber
                value={tempTarget}
                onChange={setTempTarget}
                min={0}
                max={9999}
                style={{ width: '100%', fontSize: '18px', height: '50px' }}
                placeholder="请输入目标数量"
                autoFocus
              />
              <Text type="secondary" style={{ fontSize: '12px', marginTop: '4px', display: 'block' }}>
                建议根据实际生产能力设置合理目标
              </Text>
            </div>
            
            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button 
                type="primary" 
                icon={<SaveOutlined />} 
                onClick={handleSave}
                size="large"
              >
                保存
              </Button>
              <Button 
                icon={<CloseOutlined />} 
                onClick={handleCancel}
                size="large"
              >
                取消
              </Button>
            </Space>
          </Space>
        ) : (
          <div className="target-display">
            <div className="target-number">
              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {target}
              </Text>
              <Text style={{ fontSize: '16px', marginLeft: '8px', color: '#666' }}>
                个/10分钟
              </Text>
            </div>
            
            <div className="target-description">
              <Text type="secondary" style={{ fontSize: '14px' }}>
                每10分钟的生产目标数量
              </Text>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}

export default TargetSetting
