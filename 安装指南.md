# 生产线节拍器 - 安装指南

## 快速体验（无需安装）

我已经为您创建了一个演示版本，可以直接在浏览器中运行：

1. **打开演示版本**：双击 `demo.html` 文件，在浏览器中打开
2. **功能体验**：
   - 使用"手动计数 +1"按钮模拟生产计数
   - 设置生产目标并查看完成进度
   - 观察10分钟倒计时和自动重置功能
   - 查看生产速度计算和对比

## 完整版本安装（支持串口通信）

要使用完整的串口通信功能，需要安装Node.js和相关依赖：

### 第一步：安装Node.js

1. **下载Node.js**：
   - 访问 [https://nodejs.org/](https://nodejs.org/)
   - 下载LTS版本（推荐18.x或更高版本）
   - 运行安装程序，按默认设置安装

2. **验证安装**：
   - 打开命令提示符（cmd）或PowerShell
   - 运行 `node --version` 和 `npm --version`
   - 如果显示版本号，说明安装成功

### 第二步：安装项目依赖

在项目文件夹中打开命令行，运行：

```bash
npm install
```

这将安装所有必要的依赖包，包括：
- React 18（前端框架）
- Electron 28（桌面应用框架）
- Ant Design 5（UI组件库）
- SerialPort（串口通信库）

### 第三步：运行应用

#### 开发模式（推荐用于测试）

```bash
# 启动Web版本（浏览器中运行，支持手动测试）
npm run dev

# 启动桌面应用（支持串口通信）
npm run electron-dev
```

#### 生产模式

```bash
# 构建并打包桌面应用
npm run dist
```

## 串口配置

### 硬件要求
- 可用的COM8串口（或其他串口）
- 串口参数：
  - 波特率：9600
  - 数据位：8
  - 停止位：1
  - 校验位：无

### 数据格式
- 每接收到一条以 `\r\n` 结尾的数据就计数一次
- 数据内容可以是任意文本
- 建议发送简单的计数信号，如 "1\r\n"

### 测试串口连接

如果您有Arduino或其他串口设备，可以使用以下简单代码测试：

```arduino
void setup() {
  Serial.begin(9600);
}

void loop() {
  Serial.println("1");  // 发送计数信号
  delay(2000);          // 每2秒发送一次
}
```

## 故障排除

### 常见问题

1. **npm命令不识别**
   - 确认Node.js已正确安装
   - 重启命令行窗口
   - 检查环境变量PATH设置

2. **依赖安装失败**
   - 尝试使用管理员权限运行命令行
   - 清除npm缓存：`npm cache clean --force`
   - 删除node_modules文件夹后重新安装

3. **串口连接失败**
   - 确认COM8端口存在且未被占用
   - 在设备管理器中检查串口状态
   - 尝试使用其他串口（应用支持选择不同端口）

4. **Electron应用无法启动**
   - 确认所有依赖已正确安装
   - 检查防火墙和杀毒软件设置
   - 尝试以管理员权限运行

### 性能优化建议

1. **生产环境部署**
   - 使用 `npm run dist` 构建独立的可执行文件
   - 将应用安装在生产线专用计算机上
   - 确保计算机性能稳定，避免影响计数准确性

2. **网络环境**
   - 如果需要远程监控，可以考虑添加网络功能
   - 当前版本专注于本地串口通信，确保稳定性

## 功能说明

### 核心功能
- ✅ 串口自动计数（COM8或其他端口）
- ✅ 10分钟周期自动重置
- ✅ 生产目标设置和进度显示
- ✅ 生产速度计算和对比
- ✅ 美观的现代化界面

### 扩展功能（可定制开发）
- 📊 历史数据记录和分析
- 📈 生产报表生成
- 🔔 目标达成提醒
- 🌐 网络监控功能
- 📱 移动端适配

## 技术支持

如果在安装或使用过程中遇到问题，请：

1. 首先查看本指南的故障排除部分
2. 检查控制台错误信息
3. 确认硬件连接和配置正确
4. 联系技术支持团队

## 更新日志

### v1.0.0 (当前版本)
- 基础计数功能
- 串口通信支持
- 10分钟周期管理
- 目标设置和进度显示
- 速度计算和对比
- 现代化UI设计

---

**注意**：演示版本（demo.html）可以立即使用，无需安装任何软件，适合快速体验和功能演示。完整版本需要安装Node.js环境，但提供完整的串口通信功能。
