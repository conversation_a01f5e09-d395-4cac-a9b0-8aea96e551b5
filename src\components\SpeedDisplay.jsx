import React from 'react'
import { Card, Typography, Space, Progress, Tag } from 'antd'
import { 
  DashboardOutlined, 
  ArrowUpOutlined, 
  ArrowDownOutlined, 
  MinusOutlined,
  TrophyOutlined,
  ClockCircleOutlined 
} from '@ant-design/icons'
import { formatSpeed } from '../utils/timeUtils'

const { Title, Text } = Typography

/**
 * 速度显示组件 - 显示当前和上一个周期的生产速度对比
 */
function SpeedDisplay({ currentSpeed, previousSpeed, currentCount, target }) {
  // 计算速度变化
  const getSpeedComparison = () => {
    if (previousSpeed === 0) {
      return { type: 'new', icon: <MinusOutlined />, text: '首次记录', color: '#1890ff' }
    }
    
    if (currentSpeed === 0) {
      return { type: 'none', icon: <MinusOutlined />, text: '暂无数据', color: '#999' }
    }
    
    if (currentSpeed < previousSpeed) {
      const improvement = ((previousSpeed - currentSpeed) / previousSpeed * 100).toFixed(1)
      return { 
        type: 'faster', 
        icon: <ArrowUpOutlined />, 
        text: `提速 ${improvement}%`, 
        color: '#52c41a' 
      }
    } else if (currentSpeed > previousSpeed) {
      const decline = ((currentSpeed - previousSpeed) / previousSpeed * 100).toFixed(1)
      return { 
        type: 'slower', 
        icon: <ArrowDownOutlined />, 
        text: `减速 ${decline}%`, 
        color: '#ff4d4f' 
      }
    } else {
      return { type: 'same', icon: <MinusOutlined />, text: '速度相同', color: '#1890ff' }
    }
  }

  // 计算目标完成进度
  const getTargetProgress = () => {
    if (target === 0) return 0
    return Math.min((currentCount / target) * 100, 100)
  }

  // 获取进度条颜色
  const getProgressColor = () => {
    const progress = getTargetProgress()
    if (progress >= 100) return '#52c41a'
    if (progress >= 80) return '#1890ff'
    if (progress >= 60) return '#faad14'
    return '#ff4d4f'
  }

  const speedComparison = getSpeedComparison()
  const targetProgress = getTargetProgress()

  return (
    <Card 
      className="speed-display-card"
      title={
        <Space>
          <DashboardOutlined style={{ color: '#52c41a' }} />
          <Title level={4} style={{ margin: 0, color: '#52c41a' }}>
            生产速度
          </Title>
        </Space>
      }
      bodyStyle={{ padding: '20px' }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 当前速度显示 */}
        <div className="current-speed-section">
          <div className="speed-header">
            <Space align="center">
              <ClockCircleOutlined style={{ fontSize: '18px', color: '#1890ff' }} />
              <Text strong style={{ fontSize: '16px' }}>当前周期速度</Text>
            </Space>
          </div>
          
          <div className="speed-value">
            <Text style={{ fontSize: '28px', fontWeight: 'bold', color: '#1890ff' }}>
              {formatSpeed(currentSpeed)}
            </Text>
          </div>
        </div>

        {/* 速度对比 */}
        <div className="speed-comparison-section">
          <div className="comparison-header">
            <Text strong style={{ fontSize: '14px', color: '#666' }}>
              与上个周期对比
            </Text>
          </div>
          
          <div className="comparison-content">
            <Space align="center" size="middle">
              <Tag 
                icon={speedComparison.icon} 
                color={speedComparison.color}
                style={{ fontSize: '14px', padding: '4px 12px' }}
              >
                {speedComparison.text}
              </Tag>
              
              {previousSpeed > 0 && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  上周期: {formatSpeed(previousSpeed)}
                </Text>
              )}
            </Space>
          </div>
        </div>

        {/* 目标完成进度 */}
        {target > 0 && (
          <div className="target-progress-section">
            <div className="progress-header">
              <Space align="center" justify="space-between" style={{ width: '100%' }}>
                <Space align="center">
                  <TrophyOutlined style={{ fontSize: '16px', color: '#faad14' }} />
                  <Text strong style={{ fontSize: '14px' }}>目标完成度</Text>
                </Space>
                <Text strong style={{ fontSize: '14px', color: getProgressColor() }}>
                  {currentCount}/{target}
                </Text>
              </Space>
            </div>
            
            <Progress 
              percent={targetProgress} 
              strokeColor={getProgressColor()}
              trailColor="#f0f0f0"
              strokeWidth={12}
              format={(percent) => `${percent.toFixed(1)}%`}
              style={{ marginTop: '8px' }}
            />
            
            {targetProgress >= 100 && (
              <div style={{ textAlign: 'center', marginTop: '8px' }}>
                <Tag icon={<TrophyOutlined />} color="gold" style={{ fontSize: '12px' }}>
                  🎉 目标达成！
                </Tag>
              </div>
            )}
          </div>
        )}
      </Space>
    </Card>
  )
}

export default SpeedDisplay
