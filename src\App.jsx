import React, { useState, useEffect, useCallback } from 'react'
import { Layout, Typography, message } from 'antd'
import Counter from './components/Counter'
import TargetSetting from './components/TargetSetting'
import SpeedDisplay from './components/SpeedDisplay'
import StatusBar from './components/StatusBar'
import { useTimer } from './hooks/useTimer'
import { useSerialPort } from './hooks/useSerialPort'
import { calculateSpeed } from './utils/timeUtils'

const { Header, Content } = Layout
const { Title, Text } = Typography

/**
 * 主应用组件
 */
function App() {
  // 状态管理
  const [target, setTarget] = useState(100) // 默认目标100个
  const [currentCount, setCurrentCount] = useState(0)
  const [previousCount, setPreviousCount] = useState(0)
  const [currentSpeed, setCurrentSpeed] = useState(0) // 当前周期速度（秒/个）
  const [previousSpeed, setPreviousSpeed] = useState(0) // 上个周期速度（秒/个）
  const [cycleHistory, setCycleHistory] = useState([]) // 周期历史记录

  // 自定义Hooks
  const { 
    currentCycleStart, 
    timeToNext, 
    currentTime, 
    checkCycleReset 
  } = useTimer()

  const {
    isConnected,
    currentPort,
    availablePorts,
    connectionError,
    dataCount,
    connectToPort,
    getAvailablePorts,
    resetCount,
    incrementCount,
    isElectron
  } = useSerialPort()

  // 处理目标变更
  const handleTargetChange = useCallback((newTarget) => {
    setTarget(newTarget)
    message.success(`目标已设置为 ${newTarget} 个/10分钟`)
  }, [])

  // 处理串口端口变更
  const handlePortChange = useCallback((port) => {
    // 这里可以添加端口变更逻辑
    console.log('选择端口:', port)
  }, [])

  // 刷新串口列表
  const handleRefreshPorts = useCallback(async () => {
    try {
      await getAvailablePorts()
      message.success('串口列表已刷新')
    } catch (error) {
      message.error('刷新串口列表失败')
    }
  }, [getAvailablePorts])

  // 连接串口
  const handleConnect = useCallback(async (port) => {
    try {
      const success = await connectToPort(port)
      if (success) {
        message.success(`成功连接到 ${port}`)
      } else {
        message.error(`连接 ${port} 失败`)
      }
    } catch (error) {
      message.error('连接串口时发生错误')
    }
  }, [connectToPort])

  // 手动增加计数
  const handleManualIncrement = useCallback(() => {
    incrementCount()
    message.success('计数 +1')
  }, [incrementCount])

  // 重置计数
  const handleReset = useCallback(() => {
    setCurrentCount(0)
    resetCount()
    message.info('计数已重置')
  }, [resetCount])

  // 计算当前周期速度
  const calculateCurrentSpeed = useCallback(() => {
    if (currentCount === 0) return 0
    
    const now = new Date()
    const elapsedMinutes = (now - currentCycleStart) / (1000 * 60)
    
    return calculateSpeed(currentCount, elapsedMinutes)
  }, [currentCount, currentCycleStart])

  // 周期重置处理
  const handleCycleReset = useCallback(() => {
    // 保存当前周期数据到历史
    const cycleData = {
      startTime: currentCycleStart,
      endTime: new Date(),
      count: currentCount,
      target: target,
      speed: currentSpeed
    }
    
    setCycleHistory(prev => [...prev.slice(-9), cycleData]) // 保留最近10个周期
    
    // 更新速度数据
    setPreviousSpeed(currentSpeed)
    setPreviousCount(currentCount)
    
    // 重置当前计数
    setCurrentCount(0)
    resetCount()
    
    message.info('新的10分钟周期开始！', 3)
  }, [currentCycleStart, currentCount, target, currentSpeed, resetCount])

  // 监听串口数据变化
  useEffect(() => {
    setCurrentCount(dataCount)
  }, [dataCount])

  // 监听周期重置
  useEffect(() => {
    const wasReset = checkCycleReset()
    if (wasReset) {
      handleCycleReset()
    }
  }, [checkCycleReset, handleCycleReset])

  // 定期更新当前速度
  useEffect(() => {
    const timer = setInterval(() => {
      const speed = calculateCurrentSpeed()
      setCurrentSpeed(speed)
    }, 5000) // 每5秒更新一次速度

    return () => clearInterval(timer)
  }, [calculateCurrentSpeed])

  return (
    <div className="app-container">
      {/* 应用标题 */}
      <div className="app-header">
        <Title className="app-title">生产线节拍器</Title>
        <Text className="app-subtitle">实时监控生产数量与速度，激发团队竞争力</Text>
      </div>

      {/* 状态栏 */}
      <StatusBar
        isConnected={isConnected}
        currentPort={currentPort}
        availablePorts={availablePorts}
        connectionError={connectionError}
        currentTime={currentTime}
        onPortChange={handlePortChange}
        onRefreshPorts={handleRefreshPorts}
        onConnect={handleConnect}
        isElectron={isElectron}
      />

      {/* 主要内容区域 */}
      <div className="main-content">
        <div className="left-panel">
          {/* 计数器 */}
          <Counter
            count={currentCount}
            target={target}
            currentCycleStart={currentCycleStart}
            timeToNext={timeToNext}
            onManualIncrement={handleManualIncrement}
            onReset={handleReset}
            isElectron={isElectron}
          />
        </div>

        <div className="right-panel">
          {/* 目标设置 */}
          <TargetSetting
            target={target}
            onTargetChange={handleTargetChange}
          />

          {/* 速度显示 */}
          <SpeedDisplay
            currentSpeed={currentSpeed}
            previousSpeed={previousSpeed}
            currentCount={currentCount}
            target={target}
          />
        </div>
      </div>
    </div>
  )
}

export default App
