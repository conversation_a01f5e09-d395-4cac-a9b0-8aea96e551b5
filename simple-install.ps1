# Simple Node.js Installation Script
Write-Host "=== Node.js Installation Helper ===" -ForegroundColor Green

# Check if Node.js is already installed
try {
    $nodeVersion = & node --version 2>$null
    if ($nodeVersion) {
        Write-Host "Node.js is already installed: $nodeVersion" -ForegroundColor Green
        $npmVersion = & npm --version 2>$null
        Write-Host "npm version: $npmVersion" -ForegroundColor Green
        
        Write-Host "`nProceeding to install project dependencies..." -ForegroundColor Yellow
        npm install
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "`nDependencies installed successfully!" -ForegroundColor Green
            Write-Host "You can now run:" -ForegroundColor Cyan
            Write-Host "  npm run dev        # Start web version" -ForegroundColor White
            Write-Host "  npm run electron-dev # Start desktop version" -ForegroundColor White
        } else {
            Write-Host "`nDependency installation failed. Please check the error messages above." -ForegroundColor Red
        }
        exit 0
    }
} catch {
    Write-Host "Node.js not found. Please install it manually." -ForegroundColor Yellow
}

# Node.js not installed - provide manual installation instructions
Write-Host "`nNode.js is not installed on this system." -ForegroundColor Red
Write-Host "`nPlease follow these steps to install Node.js:" -ForegroundColor Yellow
Write-Host "1. Open your web browser" -ForegroundColor White
Write-Host "2. Go to: https://nodejs.org/" -ForegroundColor White
Write-Host "3. Download the LTS version (recommended)" -ForegroundColor White
Write-Host "4. Run the installer and follow the setup wizard" -ForegroundColor White
Write-Host "5. Make sure to check 'Add to PATH' during installation" -ForegroundColor White
Write-Host "6. Restart your command prompt after installation" -ForegroundColor White
Write-Host "7. Run this script again to install project dependencies" -ForegroundColor White

Write-Host "`nOpening Node.js download page..." -ForegroundColor Yellow
Start-Process "https://nodejs.org/"

Read-Host "`nPress Enter to exit"
