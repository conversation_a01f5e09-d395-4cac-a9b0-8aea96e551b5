# 快速安装指南 - 生产线节拍器

## 🚀 方法一：自动安装（推荐）

1. **运行自动安装脚本**：
   ```powershell
   # 在当前目录右键选择"在终端中打开"或"PowerShell"
   # 运行以下命令（可能需要管理员权限）
   .\install-nodejs.ps1
   ```

2. **如果提示执行策略错误**：
   ```powershell
   # 临时允许脚本执行
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   # 然后重新运行安装脚本
   .\install-nodejs.ps1
   ```

## 🔧 方法二：手动安装

### 第一步：下载Node.js
1. 打开浏览器访问：https://nodejs.org/
2. 点击下载 **LTS版本**（推荐18.x或20.x）
3. 选择Windows Installer (.msi)

### 第二步：安装Node.js
1. 运行下载的.msi安装文件
2. 按照安装向导操作：
   - 接受许可协议
   - 选择安装路径（建议使用默认路径）
   - 确保勾选"Add to PATH"选项
   - 完成安装

### 第三步：验证安装
1. **重新打开命令行窗口**（重要！）
2. 运行验证命令：
   ```cmd
   node --version
   npm --version
   ```
3. 如果显示版本号，说明安装成功

## 📦 安装项目依赖

安装Node.js后，在项目目录运行：

```bash
# 安装所有依赖包
npm install

# 如果安装速度慢，可以使用国内镜像
npm install --registry https://registry.npmmirror.com
```

## 🎯 启动应用

### Web版本（浏览器）
```bash
npm run dev
```
然后打开浏览器访问：http://localhost:5173

### 桌面版本（支持串口）
```bash
npm run electron-dev
```

## ⚡ 快速测试步骤

1. **基础功能测试**：
   - 点击"手动计数 +1"按钮
   - 观察计数是否正常增加
   - 查看10分钟倒计时是否正常

2. **目标设置测试**：
   - 点击"编辑目标"按钮
   - 设置不同的目标值（如50、100、200）
   - 观察进度条变化

3. **速度计算测试**：
   - 连续点击计数按钮
   - 观察生产速度是否实时更新
   - 等待一段时间后查看速度对比

4. **周期重置测试**：
   - 可以手动修改系统时间来测试10分钟重置功能
   - 或者等待自然的10分钟周期

## 🔍 常见问题解决

### 问题1：npm命令不识别
**解决方案**：
- 确认Node.js安装成功
- 重启命令行窗口
- 检查环境变量PATH中是否包含Node.js路径

### 问题2：依赖安装失败
**解决方案**：
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules文件夹（如果存在）
rmdir /s node_modules

# 重新安装
npm install
```

### 问题3：权限错误
**解决方案**：
- 以管理员身份运行命令行
- 或者修改npm全局安装目录权限

### 问题4：网络连接问题
**解决方案**：
```bash
# 使用国内镜像源
npm config set registry https://registry.npmmirror.com

# 或者使用cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com
cnpm install
```

## 📋 验收检查清单

安装完成后，请确认以下功能正常：

- [ ] Node.js和npm版本显示正常
- [ ] 项目依赖安装无错误
- [ ] Web版本能够正常启动
- [ ] 桌面版本能够正常启动
- [ ] 计数功能工作正常
- [ ] 目标设置功能可用
- [ ] 10分钟倒计时显示正确
- [ ] 生产速度计算正常
- [ ] 界面显示美观，无明显错误

## 🎉 安装成功后的下一步

1. **熟悉界面**：花几分钟了解各个功能区域
2. **设置目标**：根据实际生产能力设置合理目标
3. **测试串口**：如果有串口设备，测试连接功能
4. **部署到生产线**：将应用安装到生产线计算机上

---

**提示**：如果遇到任何问题，请先查看此指南的常见问题部分。大多数安装问题都可以通过重启命令行窗口或清除缓存解决。
