# 生产线节拍器 - 完整测试计划

## 📋 测试前准备

### 环境要求检查
- [ ] Windows 10/11 操作系统
- [ ] Node.js 16+ 已安装
- [ ] npm 包管理器可用
- [ ] 至少 2GB 可用磁盘空间
- [ ] 网络连接正常（用于下载依赖）

### 安装验证命令
```bash
# 验证Node.js安装
node --version    # 应显示 v16.x.x 或更高版本
npm --version     # 应显示 8.x.x 或更高版本

# 验证项目文件
dir               # 确认package.json等文件存在
```

## 🔧 第一阶段：依赖安装测试

### 1.1 基础依赖安装
```bash
# 清理可能存在的旧依赖
rmdir /s node_modules 2>nul
del package-lock.json 2>nul

# 安装项目依赖
npm install
```

**预期结果**：
- [ ] 安装过程无错误
- [ ] 创建 node_modules 文件夹
- [ ] 生成 package-lock.json 文件
- [ ] 显示安装的包数量和时间

### 1.2 关键依赖验证
```bash
# 检查关键依赖是否正确安装
npm list react
npm list electron
npm list serialport
npm list antd
```

**预期结果**：
- [ ] React 18.x.x 已安装
- [ ] Electron 28.x.x 已安装
- [ ] SerialPort 12.x.x 已安装
- [ ] Ant Design 5.x.x 已安装

## 🌐 第二阶段：Web版本测试

### 2.1 启动Web开发服务器
```bash
npm run dev
```

**预期结果**：
- [ ] 服务器在 http://localhost:5173 启动
- [ ] 控制台无错误信息
- [ ] 显示 "Local: http://localhost:5173/"

### 2.2 Web界面功能测试

#### 基础界面检查
- [ ] 页面标题显示"生产线节拍器"
- [ ] 渐变背景正常显示
- [ ] 所有卡片组件正确渲染
- [ ] 状态栏显示"演示模式 - 串口未连接"
- [ ] 当前时间正确显示并实时更新

#### 计数器功能测试
- [ ] 初始计数显示为 0
- [ ] 点击"手动计数 +1"按钮，计数正确增加
- [ ] 连续点击多次，计数累加正确
- [ ] 点击"重置计数"按钮，计数归零
- [ ] 确认重置时弹出确认对话框

#### 目标设置功能测试
- [ ] 默认目标显示为 100
- [ ] 点击"编辑"按钮，显示输入框
- [ ] 输入新目标值（如 150），点击保存
- [ ] 目标值更新，进度条重新计算
- [ ] 点击取消按钮，恢复原值

#### 进度显示测试
- [ ] 进度条根据计数/目标正确显示百分比
- [ ] 进度文本显示格式："当前数量/目标数量 (百分比%)"
- [ ] 达到目标时进度条变为绿色
- [ ] 超过目标时进度条保持100%

#### 10分钟周期测试
- [ ] 周期开始时间显示正确（最近的10分钟整点）
- [ ] 倒计时正确显示剩余时间（格式：MM:SS）
- [ ] 倒计时每秒更新
- [ ] 可以通过修改系统时间测试周期重置

#### 速度计算测试
- [ ] 初始速度显示"0秒/个"
- [ ] 开始计数后，速度实时更新
- [ ] 速度格式正确（秒/个 或 分钟秒/个）
- [ ] 速度对比显示"首次记录"

## 🖥️ 第三阶段：桌面版本测试

### 3.1 启动Electron应用
```bash
npm run electron-dev
```

**预期结果**：
- [ ] Electron窗口正常打开
- [ ] 窗口标题显示"生产线节拍器"
- [ ] 窗口大小合适（1200x800）
- [ ] 界面与Web版本一致

### 3.2 串口功能测试

#### 串口连接界面
- [ ] 状态栏显示串口连接状态
- [ ] 串口选择下拉框可用
- [ ] 刷新按钮可以更新串口列表
- [ ] 连接按钮可以点击

#### 串口连接测试（如果有可用串口）
- [ ] 选择可用串口（如COM1, COM3等）
- [ ] 点击连接按钮
- [ ] 连接成功时状态变为绿色
- [ ] 连接失败时显示错误信息

#### 串口数据接收测试（需要外部设备）
- [ ] 连接串口设备
- [ ] 发送测试数据
- [ ] 应用正确接收并计数
- [ ] 每接收一条数据计数+1

### 3.3 桌面应用特有功能
- [ ] 窗口可以最小化/最大化
- [ ] 应用可以正常关闭
- [ ] 重新打开时保持设置
- [ ] 开发者工具可以打开（F12）

## 🚀 第四阶段：性能和稳定性测试

### 4.1 性能测试
- [ ] 连续快速点击计数按钮100次，应用响应正常
- [ ] 长时间运行（30分钟），内存使用稳定
- [ ] 界面动画流畅，无卡顿
- [ ] CPU使用率合理（<10%）

### 4.2 边界条件测试
- [ ] 设置目标为0，应用正常工作
- [ ] 设置目标为9999，应用正常工作
- [ ] 计数达到很大数值（如10000），显示正常
- [ ] 快速切换目标值，计算正确

### 4.3 错误处理测试
- [ ] 输入非法目标值，显示错误提示
- [ ] 网络断开时，本地功能正常
- [ ] 串口设备断开时，应用不崩溃
- [ ] 异常关闭后重启，应用正常

## 📊 第五阶段：用户体验测试

### 5.1 界面美观性
- [ ] 色彩搭配协调，符合工业风格
- [ ] 字体大小合适，易于阅读
- [ ] 按钮大小合适，易于点击
- [ ] 动画效果自然，不过于花哨

### 5.2 易用性测试
- [ ] 新用户可以快速理解界面功能
- [ ] 操作流程直观，无需说明书
- [ ] 重要信息突出显示
- [ ] 错误提示清晰易懂

### 5.3 响应式设计测试
- [ ] 在不同分辨率下显示正常
- [ ] 窗口缩放时布局自适应
- [ ] 在高DPI显示器上清晰显示

## 🎯 验收标准

### 必须通过的测试项
1. **基础功能**：计数、目标设置、进度显示
2. **时间管理**：10分钟周期、倒计时、自动重置
3. **速度计算**：实时速度、历史对比
4. **界面质量**：美观、响应式、无明显bug
5. **稳定性**：长时间运行无崩溃

### 可选功能（如果硬件支持）
1. **串口通信**：连接、数据接收、状态显示
2. **桌面应用**：Electron功能正常

## 📝 测试报告模板

### 测试环境
- 操作系统：Windows [版本]
- Node.js版本：[版本号]
- 浏览器：[浏览器及版本]
- 测试时间：[日期时间]

### 测试结果
- 通过测试项：[数量]/[总数]
- 失败测试项：[列出具体项目]
- 发现的问题：[详细描述]
- 建议改进：[具体建议]

### 总体评价
- [ ] 完全满足需求，可以投入生产使用
- [ ] 基本满足需求，有小问题但不影响使用
- [ ] 部分满足需求，需要修复关键问题
- [ ] 不满足需求，需要重大修改

---

**注意**：请按照此测试计划逐项进行测试，并记录每个测试项的结果。如果发现问题，请详细记录错误信息和重现步骤。
