import { app, BrowserWindow, ipcMain } from 'electron'
import { fileURLToPath } from 'url'
import path from 'path'
import { SerialPort } from 'serialport'
import { ReadlineParser } from '@serialport/parser-readline'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

let mainWindow
let serialPort = null
let parser = null

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/icon.png'),
    title: '生产线节拍器',
    show: false
  })

  // 开发环境加载开发服务器，生产环境加载构建文件
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:5173')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.on('closed', () => {
    if (serialPort && serialPort.isOpen) {
      serialPort.close()
    }
    mainWindow = null
  })
}

// 初始化串口连接
async function initSerialPort() {
  try {
    // 列出所有可用端口
    const ports = await SerialPort.list()
    console.log('可用端口:', ports)

    // 尝试连接COM8
    serialPort = new SerialPort({
      path: 'COM8',
      baudRate: 9600,
      dataBits: 8,
      parity: 'none',
      stopBits: 1
    })

    parser = serialPort.pipe(new ReadlineParser({ delimiter: '\r\n' }))

    serialPort.on('open', () => {
      console.log('串口COM8已打开')
      if (mainWindow) {
        mainWindow.webContents.send('serial-status', { connected: true, port: 'COM8' })
      }
    })

    serialPort.on('error', (err) => {
      console.error('串口错误:', err.message)
      if (mainWindow) {
        mainWindow.webContents.send('serial-status', { connected: false, error: err.message })
      }
    })

    parser.on('data', (data) => {
      console.log('接收到数据:', data)
      if (mainWindow) {
        mainWindow.webContents.send('serial-data', data.trim())
      }
    })

  } catch (error) {
    console.error('串口初始化失败:', error)
    if (mainWindow) {
      mainWindow.webContents.send('serial-status', { connected: false, error: error.message })
    }
  }
}

// IPC 事件处理
ipcMain.handle('get-serial-ports', async () => {
  try {
    const ports = await SerialPort.list()
    return ports
  } catch (error) {
    console.error('获取串口列表失败:', error)
    return []
  }
})

ipcMain.handle('connect-serial', async (event, portPath) => {
  try {
    if (serialPort && serialPort.isOpen) {
      serialPort.close()
    }

    serialPort = new SerialPort({
      path: portPath,
      baudRate: 9600,
      dataBits: 8,
      parity: 'none',
      stopBits: 1
    })

    parser = serialPort.pipe(new ReadlineParser({ delimiter: '\r\n' }))

    return new Promise((resolve, reject) => {
      serialPort.on('open', () => {
        console.log(`串口${portPath}已打开`)
        parser.on('data', (data) => {
          console.log('接收到数据:', data)
          if (mainWindow) {
            mainWindow.webContents.send('serial-data', data.trim())
          }
        })
        resolve({ success: true, port: portPath })
      })

      serialPort.on('error', (err) => {
        console.error('串口错误:', err.message)
        reject({ success: false, error: err.message })
      })
    })
  } catch (error) {
    console.error('连接串口失败:', error)
    return { success: false, error: error.message }
  }
})

app.whenReady().then(() => {
  createWindow()
  
  // 延迟初始化串口，等待窗口完全加载
  setTimeout(() => {
    initSerialPort()
  }, 2000)

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (serialPort && serialPort.isOpen) {
    serialPort.close()
  }
  if (process.platform !== 'darwin') {
    app.quit()
  }
})
