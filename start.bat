@echo off
chcp 65001 >nul
title 生产线节拍器 - 启动助手

echo.
echo ========================================
echo    生产线节拍器 - 启动助手
echo ========================================
echo.

:: 检查Node.js是否安装
echo [1/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装
    echo.
    echo 请先安装Node.js：
    echo 1. 访问 https://nodejs.org/
    echo 2. 下载LTS版本
    echo 3. 运行安装程序
    echo 4. 重启命令行后再次运行此脚本
    echo.
    echo 正在打开Node.js下载页面...
    start https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js 已安装: %NODE_VERSION%
)

:: 检查npm是否可用
echo [2/4] 检查npm包管理器...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 不可用
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm 已安装: v%NPM_VERSION%
)

:: 检查依赖是否已安装
echo [3/4] 检查项目依赖...
if not exist "node_modules" (
    echo ⚠️  依赖未安装，开始安装...
    echo.
    echo 正在安装项目依赖，请稍候...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        echo.
        echo 可能的解决方案：
        echo 1. 检查网络连接
        echo 2. 尝试使用管理员权限运行
        echo 3. 清除npm缓存: npm cache clean --force
        pause
        exit /b 1
    ) else (
        echo ✅ 依赖安装成功
    )
) else (
    echo ✅ 依赖已安装
)

:: 显示启动选项
echo [4/4] 选择启动方式...
echo.
echo 请选择要启动的版本：
echo.
echo 1. Web版本 (浏览器中运行，支持手动测试)
echo 2. 桌面版本 (Electron应用，支持串口通信)
echo 3. 打开演示页面 (无需安装，立即体验)
echo 4. 查看项目文档
echo 5. 运行测试
echo 0. 退出
echo.
set /p choice="请输入选择 (0-5): "

if "%choice%"=="1" goto web_version
if "%choice%"=="2" goto desktop_version
if "%choice%"=="3" goto demo_version
if "%choice%"=="4" goto documentation
if "%choice%"=="5" goto run_tests
if "%choice%"=="0" goto exit
goto invalid_choice

:web_version
echo.
echo 🌐 启动Web版本...
echo 浏览器将自动打开 http://localhost:5173
echo 按 Ctrl+C 停止服务器
echo.
npm run dev
goto end

:desktop_version
echo.
echo 🖥️  启动桌面版本...
echo Electron应用窗口将打开
echo.
npm run electron-dev
goto end

:demo_version
echo.
echo 🎯 打开演示页面...
start demo.html
echo ✅ 演示页面已在浏览器中打开
goto menu

:documentation
echo.
echo 📚 打开项目文档...
if exist "README.md" start README.md
if exist "快速安装指南.md" start "快速安装指南.md"
if exist "测试计划.md" start "测试计划.md"
echo ✅ 文档已打开
goto menu

:run_tests
echo.
echo 🧪 运行测试...
echo 请按照测试计划进行手动测试
if exist "测试计划.md" start "测试计划.md"
echo.
echo 基础功能测试：
echo 1. 启动应用
echo 2. 测试计数功能
echo 3. 测试目标设置
echo 4. 测试10分钟周期
echo 5. 测试速度计算
goto menu

:invalid_choice
echo.
echo ❌ 无效选择，请重新输入
goto menu

:menu
echo.
echo 按任意键返回主菜单...
pause >nul
goto start_menu

:start_menu
cls
goto :eof

:exit
echo.
echo 👋 感谢使用生产线节拍器！
echo.

:end
pause
