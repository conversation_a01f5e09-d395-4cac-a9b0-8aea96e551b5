import { contextBridge, ipc<PERSON>enderer } from 'electron'

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 串口相关API
  getSerialPorts: () => ipcRenderer.invoke('get-serial-ports'),
  connectSerial: (portPath) => ipcRenderer.invoke('connect-serial', portPath),
  
  // 监听串口数据
  onSerialData: (callback) => {
    ipcRenderer.on('serial-data', (event, data) => callback(data))
  },
  
  // 监听串口状态
  onSerialStatus: (callback) => {
    ipcRenderer.on('serial-status', (event, status) => callback(status))
  },
  
  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  }
})
