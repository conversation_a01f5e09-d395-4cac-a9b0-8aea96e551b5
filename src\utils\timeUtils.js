// 时间工具函数

/**
 * 获取当前10分钟周期的开始时间
 * @returns {Date} 当前10分钟周期的开始时间
 */
export function getCurrentCycleStart() {
  const now = new Date()
  const minutes = now.getMinutes()
  const cycleMinutes = Math.floor(minutes / 10) * 10
  
  const cycleStart = new Date(now)
  cycleStart.setMinutes(cycleMinutes, 0, 0) // 设置秒和毫秒为0
  
  return cycleStart
}

/**
 * 获取下一个10分钟周期的开始时间
 * @returns {Date} 下一个10分钟周期的开始时间
 */
export function getNextCycleStart() {
  const currentStart = getCurrentCycleStart()
  const nextStart = new Date(currentStart)
  nextStart.setMinutes(nextStart.getMinutes() + 10)
  
  return nextStart
}

/**
 * 计算距离下一个周期开始的剩余时间（毫秒）
 * @returns {number} 剩余毫秒数
 */
export function getTimeToNextCycle() {
  const now = new Date()
  const nextStart = getNextCycleStart()
  
  return nextStart.getTime() - now.getTime()
}

/**
 * 格式化时间显示
 * @param {Date} date 要格式化的时间
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date) {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 格式化倒计时显示
 * @param {number} milliseconds 毫秒数
 * @returns {string} 格式化后的倒计时字符串
 */
export function formatCountdown(milliseconds) {
  const totalSeconds = Math.ceil(milliseconds / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

/**
 * 计算生产速度（每个产品的平均耗时）
 * @param {number} count 产品数量
 * @param {number} timeInMinutes 时间（分钟）
 * @returns {number} 每个产品的平均耗时（秒）
 */
export function calculateSpeed(count, timeInMinutes) {
  if (count === 0) return 0
  return (timeInMinutes * 60) / count
}

/**
 * 格式化速度显示
 * @param {number} speedInSeconds 速度（秒/个）
 * @returns {string} 格式化后的速度字符串
 */
export function formatSpeed(speedInSeconds) {
  if (speedInSeconds === 0) return '0秒/个'
  
  if (speedInSeconds >= 60) {
    const minutes = Math.floor(speedInSeconds / 60)
    const seconds = Math.round(speedInSeconds % 60)
    return `${minutes}分${seconds}秒/个`
  } else {
    return `${Math.round(speedInSeconds)}秒/个`
  }
}
