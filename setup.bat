@echo off
title Production Metronome Setup

echo.
echo ========================================
echo    Production Metronome Setup
echo ========================================
echo.

echo [1/4] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed
    echo.
    echo Please install Node.js first:
    echo 1. Visit https://nodejs.org/
    echo 2. Download LTS version
    echo 3. Run the installer
    echo 4. Restart command prompt and run this script again
    echo.
    echo Opening Node.js download page...
    start https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo SUCCESS: Node.js installed: %NODE_VERSION%
)

echo [2/4] Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo SUCCESS: npm installed: v%NPM_VERSION%
)

echo [3/4] Installing dependencies...
if not exist "node_modules" (
    echo Installing project dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        echo.
        echo Possible solutions:
        echo 1. Check internet connection
        echo 2. Run as administrator
        echo 3. Clear npm cache: npm cache clean --force
        pause
        exit /b 1
    ) else (
        echo SUCCESS: Dependencies installed
    )
) else (
    echo SUCCESS: Dependencies already installed
)

echo [4/4] Setup complete!
echo.
echo Choose how to start the application:
echo.
echo 1. Web version (browser, manual testing)
echo 2. Desktop version (Electron, serial port support)
echo 3. Demo page (no installation required)
echo 0. Exit
echo.
set /p choice="Enter your choice (0-3): "

if "%choice%"=="1" goto web
if "%choice%"=="2" goto desktop
if "%choice%"=="3" goto demo
if "%choice%"=="0" goto exit

:web
echo.
echo Starting web version...
echo Browser will open at http://localhost:5173
echo Press Ctrl+C to stop the server
echo.
npm run dev
goto end

:desktop
echo.
echo Starting desktop version...
echo Electron window will open
echo.
npm run electron-dev
goto end

:demo
echo.
echo Opening demo page...
start demo.html
echo Demo page opened in browser
goto end

:exit
echo.
echo Thank you for using Production Metronome!
echo.

:end
pause
