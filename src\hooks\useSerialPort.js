import { useState, useEffect, useCallback } from 'react'

/**
 * 串口通信Hook
 * @returns {Object} 串口相关状态和方法
 */
export function useSerialPort() {
  const [isConnected, setIsConnected] = useState(false)
  const [currentPort, setCurrentPort] = useState('COM8')
  const [availablePorts, setAvailablePorts] = useState([])
  const [connectionError, setConnectionError] = useState(null)
  const [dataCount, setDataCount] = useState(0)
  const [lastDataTime, setLastDataTime] = useState(null)

  // 检查是否在Electron环境中
  const isElectron = typeof window !== 'undefined' && window.electronAPI

  // 获取可用串口列表
  const getAvailablePorts = useCallback(async () => {
    if (!isElectron) return []
    
    try {
      const ports = await window.electronAPI.getSerialPorts()
      setAvailablePorts(ports)
      return ports
    } catch (error) {
      console.error('获取串口列表失败:', error)
      setConnectionError(error.message)
      return []
    }
  }, [isElectron])

  // 连接串口
  const connectToPort = useCallback(async (portPath) => {
    if (!isElectron) {
      console.warn('不在Electron环境中，无法连接串口')
      return false
    }

    try {
      setConnectionError(null)
      const result = await window.electronAPI.connectSerial(portPath)
      
      if (result.success) {
        setIsConnected(true)
        setCurrentPort(portPath)
        console.log(`成功连接到串口: ${portPath}`)
        return true
      } else {
        setConnectionError(result.error)
        setIsConnected(false)
        console.error('连接串口失败:', result.error)
        return false
      }
    } catch (error) {
      setConnectionError(error.message)
      setIsConnected(false)
      console.error('连接串口异常:', error)
      return false
    }
  }, [isElectron])

  // 重置计数
  const resetCount = useCallback(() => {
    setDataCount(0)
    setLastDataTime(null)
  }, [])

  // 手动增加计数（用于测试）
  const incrementCount = useCallback(() => {
    setDataCount(prev => prev + 1)
    setLastDataTime(new Date())
  }, [])

  useEffect(() => {
    if (!isElectron) return

    // 监听串口数据
    const handleSerialData = (data) => {
      console.log('接收到串口数据:', data)
      setDataCount(prev => prev + 1)
      setLastDataTime(new Date())
    }

    // 监听串口状态
    const handleSerialStatus = (status) => {
      console.log('串口状态更新:', status)
      setIsConnected(status.connected)
      
      if (status.connected) {
        setConnectionError(null)
        setCurrentPort(status.port)
      } else {
        setConnectionError(status.error)
      }
    }

    // 注册事件监听器
    window.electronAPI.onSerialData(handleSerialData)
    window.electronAPI.onSerialStatus(handleSerialStatus)

    // 初始化时获取可用端口
    getAvailablePorts()

    // 清理函数
    return () => {
      if (window.electronAPI.removeAllListeners) {
        window.electronAPI.removeAllListeners('serial-data')
        window.electronAPI.removeAllListeners('serial-status')
      }
    }
  }, [isElectron, getAvailablePorts])

  return {
    isConnected,
    currentPort,
    availablePorts,
    connectionError,
    dataCount,
    lastDataTime,
    connectToPort,
    getAvailablePorts,
    resetCount,
    incrementCount,
    isElectron
  }
}
