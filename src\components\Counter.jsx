import React from 'react'
import { Card, Typography, Space, Button, Progress, Tag, Statistic } from 'antd'
import { 
  PlusOutlined, 
  ReloadOutlined, 
  PlayCircleOutlined,
  ClockCircleOutlined,
  NumberOutlined 
} from '@ant-design/icons'
import { formatCountdown, formatTime } from '../utils/timeUtils'

const { Title, Text } = Typography

/**
 * 计数器组件 - 显示当前计数、倒计时等核心信息
 */
function Counter({ 
  count, 
  target, 
  currentCycleStart, 
  timeToNext, 
  onManualIncrement, 
  onReset,
  isElectron 
}) {
  // 计算完成百分比
  const getCompletionPercentage = () => {
    if (target === 0) return 0
    return Math.min((count / target) * 100, 100)
  }

  // 获取进度条颜色
  const getProgressColor = () => {
    const percentage = getCompletionPercentage()
    if (percentage >= 100) return '#52c41a'
    if (percentage >= 80) return '#1890ff'
    if (percentage >= 60) return '#faad14'
    return '#ff4d4f'
  }

  // 获取状态标签
  const getStatusTag = () => {
    const percentage = getCompletionPercentage()
    if (percentage >= 100) {
      return <Tag color="success" icon={<PlayCircleOutlined />}>目标达成</Tag>
    } else if (percentage >= 80) {
      return <Tag color="processing" icon={<PlayCircleOutlined />}>接近目标</Tag>
    } else if (percentage >= 50) {
      return <Tag color="warning" icon={<PlayCircleOutlined />}>进行中</Tag>
    } else {
      return <Tag color="default" icon={<PlayCircleOutlined />}>开始生产</Tag>
    }
  }

  const completionPercentage = getCompletionPercentage()

  return (
    <Card 
      className="counter-card"
      title={
        <Space>
          <NumberOutlined style={{ color: '#1890ff' }} />
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            生产计数器
          </Title>
        </Space>
      }
      extra={getStatusTag()}
      bodyStyle={{ padding: '30px' }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 主要计数显示 */}
        <div className="main-counter-section">
          <div className="counter-display">
            <Statistic
              title="当前产量"
              value={count}
              suffix="个"
              valueStyle={{ 
                fontSize: '72px', 
                fontWeight: 'bold', 
                color: '#1890ff',
                textAlign: 'center'
              }}
              style={{ textAlign: 'center' }}
            />
          </div>

          {/* 目标对比 */}
          {target > 0 && (
            <div className="target-comparison">
              <Space direction="vertical" style={{ width: '100%' }} size="small">
                <div style={{ textAlign: 'center' }}>
                  <Text style={{ fontSize: '18px', color: '#666' }}>
                    目标: {target}个 | 差距: {Math.max(0, target - count)}个
                  </Text>
                </div>
                
                <Progress 
                  percent={completionPercentage} 
                  strokeColor={getProgressColor()}
                  trailColor="#f0f0f0"
                  strokeWidth={16}
                  format={(percent) => `${percent.toFixed(1)}%`}
                  style={{ marginTop: '16px' }}
                />
              </Space>
            </div>
          )}
        </div>

        {/* 周期信息 */}
        <div className="cycle-info-section">
          <Card size="small" style={{ backgroundColor: '#f8f9fa' }}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div className="cycle-time-info">
                <Space justify="space-between" style={{ width: '100%' }}>
                  <Space align="center">
                    <ClockCircleOutlined style={{ color: '#52c41a' }} />
                    <Text strong>周期开始时间:</Text>
                    <Text style={{ color: '#52c41a', fontSize: '16px' }}>
                      {formatTime(currentCycleStart)}
                    </Text>
                  </Space>
                </Space>
              </div>

              <div className="countdown-info">
                <Space justify="space-between" style={{ width: '100%' }}>
                  <Space align="center">
                    <ClockCircleOutlined style={{ color: '#ff4d4f' }} />
                    <Text strong>距离重置:</Text>
                    <Text style={{ color: '#ff4d4f', fontSize: '20px', fontWeight: 'bold' }}>
                      {formatCountdown(timeToNext)}
                    </Text>
                  </Space>
                </Space>
              </div>
            </Space>
          </Card>
        </div>

        {/* 操作按钮 */}
        <div className="counter-actions">
          <Space style={{ width: '100%', justifyContent: 'center' }} size="large">
            {!isElectron && (
              <Button 
                type="primary" 
                size="large" 
                icon={<PlusOutlined />}
                onClick={onManualIncrement}
                style={{ minWidth: '120px' }}
              >
                手动计数
              </Button>
            )}
            
            <Button 
              size="large" 
              icon={<ReloadOutlined />}
              onClick={onReset}
              style={{ minWidth: '120px' }}
            >
              重置计数
            </Button>
          </Space>
          
          {!isElectron && (
            <div style={{ textAlign: 'center', marginTop: '12px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                在浏览器环境中，可使用手动计数进行测试
              </Text>
            </div>
          )}
        </div>
      </Space>
    </Card>
  )
}

export default Counter
