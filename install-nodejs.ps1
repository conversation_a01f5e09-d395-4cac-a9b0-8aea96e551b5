# Node.js Auto Installation Script
# This script will download and install the latest Node.js LTS version

Write-Host "=== Node.js Auto Installation Script ===" -ForegroundColor Green
Write-Host "Checking system environment..." -ForegroundColor Yellow

# Check if Node.js is already installed
try {
    $nodeVersion = & node --version 2>$null
    if ($nodeVersion) {
        Write-Host "Detected installed Node.js version: $nodeVersion" -ForegroundColor Green
        Write-Host "If you need to update, please uninstall manually and re-run this script." -ForegroundColor Yellow
        exit 0
    }
} catch {
    Write-Host "Node.js not detected, starting installation..." -ForegroundColor Yellow
}

# 检查是否有管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "警告: 建议以管理员权限运行此脚本以确保正确安装。" -ForegroundColor Red
    $continue = Read-Host "是否继续安装? (y/n)"
    if ($continue -ne 'y' -and $continue -ne 'Y') {
        exit 1
    }
}

# 设置下载参数
$nodeVersion = "18.19.0"  # LTS版本
$architecture = if ([Environment]::Is64BitOperatingSystem) { "x64" } else { "x86" }
$fileName = "node-v$nodeVersion-win-$architecture.msi"
$downloadUrl = "https://nodejs.org/dist/v$nodeVersion/$fileName"
$downloadPath = "$env:TEMP\$fileName"

Write-Host "下载信息:" -ForegroundColor Cyan
Write-Host "  版本: Node.js v$nodeVersion (LTS)" -ForegroundColor White
Write-Host "  架构: $architecture" -ForegroundColor White
Write-Host "  下载地址: $downloadUrl" -ForegroundColor White
Write-Host "  保存位置: $downloadPath" -ForegroundColor White

# 下载Node.js安装包
Write-Host "`n正在下载Node.js安装包..." -ForegroundColor Yellow
try {
    # 使用Invoke-WebRequest下载文件
    $ProgressPreference = 'SilentlyContinue'  # 隐藏进度条以提高性能
    Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing
    Write-Host "下载完成!" -ForegroundColor Green
} catch {
    Write-Host "下载失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动下载并安装Node.js:" -ForegroundColor Yellow
    Write-Host "1. 访问 https://nodejs.org/" -ForegroundColor White
    Write-Host "2. 下载LTS版本" -ForegroundColor White
    Write-Host "3. 运行安装程序" -ForegroundColor White
    exit 1
}

# 验证下载的文件
if (-not (Test-Path $downloadPath)) {
    Write-Host "错误: 下载的文件不存在" -ForegroundColor Red
    exit 1
}

$fileSize = (Get-Item $downloadPath).Length / 1MB
Write-Host "文件大小: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan

# 安装Node.js
Write-Host "`n正在安装Node.js..." -ForegroundColor Yellow
try {
    # 使用msiexec静默安装
    $installArgs = "/i `"$downloadPath`" /quiet /norestart ADDLOCAL=ALL"
    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $installArgs -Wait -PassThru
    
    if ($process.ExitCode -eq 0) {
        Write-Host "Node.js安装成功!" -ForegroundColor Green
    } else {
        Write-Host "安装失败，退出代码: $($process.ExitCode)" -ForegroundColor Red
        Write-Host "请尝试手动安装或以管理员权限运行此脚本。" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "安装过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 清理下载的文件
Write-Host "`n清理临时文件..." -ForegroundColor Yellow
try {
    Remove-Item $downloadPath -Force
    Write-Host "临时文件已清理" -ForegroundColor Green
} catch {
    Write-Host "警告: 无法删除临时文件 $downloadPath" -ForegroundColor Yellow
}

# 刷新环境变量
Write-Host "`n刷新环境变量..." -ForegroundColor Yellow
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

# 验证安装
Write-Host "`n验证安装..." -ForegroundColor Yellow
Start-Sleep -Seconds 3  # 等待安装完成

try {
    $nodeVersion = & node --version 2>$null
    $npmVersion = & npm --version 2>$null
    
    if ($nodeVersion -and $npmVersion) {
        Write-Host "安装验证成功!" -ForegroundColor Green
        Write-Host "  Node.js版本: $nodeVersion" -ForegroundColor White
        Write-Host "  npm版本: v$npmVersion" -ForegroundColor White
    } else {
        Write-Host "警告: 安装可能未完全成功" -ForegroundColor Yellow
        Write-Host "请重启命令行窗口后再次验证" -ForegroundColor Yellow
    }
} catch {
    Write-Host "验证时出现错误，请重启命令行窗口后手动验证:" -ForegroundColor Yellow
    Write-Host "  node --version" -ForegroundColor White
    Write-Host "  npm --version" -ForegroundColor White
}

Write-Host "`n=== 安装完成 ===" -ForegroundColor Green
Write-Host "下一步:" -ForegroundColor Cyan
Write-Host "1. 重启命令行窗口" -ForegroundColor White
Write-Host "2. 运行 'node --version' 验证安装" -ForegroundColor White
Write-Host "3. 在项目目录运行 'npm install' 安装依赖" -ForegroundColor White
Write-Host "4. 运行 'npm run dev' 启动应用" -ForegroundColor White

Read-Host "`n按任意键退出"
