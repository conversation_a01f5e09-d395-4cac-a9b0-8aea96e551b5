{"name": "production-metronome", "version": "1.0.0", "description": "生产线节拍器 - 用于记录生产数量和速度的桌面应用", "main": "electron/main.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "keywords": ["metronome", "production", "counter", "serial-port", "electron"], "author": "Production Team", "license": "MIT", "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.1.0", "electron-builder": "^24.9.1", "vite": "^5.0.8", "wait-on": "^7.2.0"}, "dependencies": {"antd": "^5.12.8", "react": "^18.2.0", "react-dom": "^18.2.0", "serialport": "^12.0.0", "@ant-design/icons": "^5.2.6"}, "build": {"appId": "com.production.metronome", "productName": "生产线节拍器", "directories": {"output": "dist"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "public/icon.ico"}}}