import { useState, useEffect, useCallback } from 'react'
import { getCurrentCycleStart, getNextCycleStart, getTimeToNextCycle } from '../utils/timeUtils'

/**
 * 10分钟周期定时器Hook
 * @returns {Object} 定时器相关状态和方法
 */
export function useTimer() {
  const [currentCycleStart, setCurrentCycleStart] = useState(getCurrentCycleStart())
  const [nextCycleStart, setNextCycleStart] = useState(getNextCycleStart())
  const [timeToNext, setTimeToNext] = useState(getTimeToNextCycle())
  const [currentTime, setCurrentTime] = useState(new Date())

  // 更新周期时间
  const updateCycleTimes = useCallback(() => {
    const now = new Date()
    const currentStart = getCurrentCycleStart()
    const nextStart = getNextCycleStart()
    
    setCurrentCycleStart(currentStart)
    setNextCycleStart(nextStart)
    setCurrentTime(now)
  }, [])

  // 检查是否需要重置周期
  const checkCycleReset = useCallback(() => {
    const now = new Date()
    
    // 如果当前时间已经超过了下一个周期开始时间，说明需要重置
    if (now >= nextCycleStart) {
      updateCycleTimes()
      return true
    }
    return false
  }, [nextCycleStart, updateCycleTimes])

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date()
      setCurrentTime(now)
      
      // 更新到下一个周期的剩余时间
      const remaining = getTimeToNextCycle()
      setTimeToNext(remaining)
      
      // 检查是否需要重置周期
      checkCycleReset()
    }, 1000)

    return () => clearInterval(timer)
  }, [checkCycleReset])

  // 手动重置周期（用于测试或特殊情况）
  const resetCycle = useCallback(() => {
    updateCycleTimes()
    setTimeToNext(getTimeToNextCycle())
  }, [updateCycleTimes])

  return {
    currentCycleStart,
    nextCycleStart,
    timeToNext,
    currentTime,
    resetCycle,
    checkCycleReset
  }
}
