import React from 'react'
import { Card, Badge, Typography, Space, Button, Select, Tooltip } from 'antd'
import { WifiOutlined, DisconnectOutlined, ReloadOutlined, UsbOutlined } from '@ant-design/icons'
import { formatTime } from '../utils/timeUtils'

const { Text } = Typography

/**
 * 状态栏组件 - 显示串口连接状态、当前时间等信息
 */
function StatusBar({ 
  isConnected, 
  currentPort, 
  availablePorts, 
  connectionError, 
  currentTime,
  onPortChange,
  onRefreshPorts,
  onConnect,
  isElectron 
}) {
  return (
    <Card 
      size="small" 
      className="status-bar"
      bodyStyle={{ padding: '12px 16px' }}
    >
      <div className="status-bar-content">
        <Space size="large" align="center">
          {/* 串口连接状态 */}
          <Space align="center">
            <Badge 
              status={isConnected ? "success" : "error"} 
              text={
                <Text strong>
                  {isConnected ? `已连接 ${currentPort}` : '未连接'}
                </Text>
              }
            />
            {isConnected ? (
              <WifiOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
            ) : (
              <DisconnectOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
            )}
          </Space>

          {/* 串口选择和连接 */}
          {isElectron && (
            <Space align="center">
              <UsbOutlined style={{ fontSize: '16px', color: '#1890ff' }} />
              <Select
                value={currentPort}
                onChange={onPortChange}
                style={{ width: 120 }}
                size="small"
                placeholder="选择串口"
              >
                {availablePorts.map(port => (
                  <Select.Option key={port.path} value={port.path}>
                    {port.path}
                  </Select.Option>
                ))}
              </Select>
              
              <Tooltip title="刷新串口列表">
                <Button 
                  icon={<ReloadOutlined />} 
                  size="small" 
                  onClick={onRefreshPorts}
                />
              </Tooltip>
              
              <Button 
                type="primary" 
                size="small" 
                onClick={() => onConnect(currentPort)}
                disabled={!currentPort}
              >
                连接
              </Button>
            </Space>
          )}

          {/* 错误信息 */}
          {connectionError && (
            <Text type="danger" style={{ fontSize: '12px' }}>
              错误: {connectionError}
            </Text>
          )}
        </Space>

        {/* 右侧时间显示 */}
        <div className="status-time">
          <Space align="center">
            <Text type="secondary">当前时间:</Text>
            <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
              {formatTime(currentTime)}
            </Text>
          </Space>
        </div>
      </div>
    </Card>
  )
}

export default StatusBar
